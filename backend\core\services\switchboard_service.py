# backend/core/services/switchboard_service.py
"""
Switchboard Service

This module provides business logic for switchboard operations, including
electrical distribution management, load balancing, and component integration.
"""

from typing import Any, Dict, List, Optional

from sqlalchemy.orm import Session

try:
    from backend.config.logging_config import get_logger
    from backend.core.errors.exceptions import (
        BaseApplicationException,
        BusinessLogicError,
        NotFoundError,
    )
    from backend.core.repositories.switchboard_repository import (
        FeederComponentRepository,
        FeederRepository,
        SwitchboardComponentRepository,
        SwitchboardRepository,
    )
    from backend.core.schemas.switchboard_schemas import (
        FeederComponentCreateSchema,
        FeederComponentReadSchema,
        FeederComponentUpdateSchema,
        FeederCreateSchema,
        FeederReadSchema,
        FeederUpdateSchema,
        SwitchboardCapacityAnalysisSchema,
        SwitchboardComponentCreateSchema,
        SwitchboardComponentReadSchema,
        SwitchboardComponentUpdateSchema,
        SwitchboardCreateSchema,
        SwitchboardLoadSummarySchema,
        SwitchboardReadSchema,
        SwitchboardUpdateSchema,
    )
    from backend.core.services.electrical_service import ElectricalService
    from backend.core.services.component_service import ComponentService
except ImportError:
    # For testing and relative imports
    import sys
    import os

    sys.path.insert(
        0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
    from config.logging_config import get_logger
    from core.errors.exceptions import (
        BaseApplicationException,
        BusinessLogicError,
        NotFoundError,
    )
    from core.repositories.switchboard_repository import (
        FeederComponentRepository,
        FeederRepository,
        SwitchboardComponentRepository,
        SwitchboardRepository,
    )
    from core.schemas.switchboard_schemas import (
        FeederComponentCreateSchema,
        FeederComponentReadSchema,
        FeederComponentUpdateSchema,
        FeederCreateSchema,
        FeederReadSchema,
        FeederUpdateSchema,
        SwitchboardCapacityAnalysisSchema,
        SwitchboardComponentCreateSchema,
        SwitchboardComponentReadSchema,
        SwitchboardComponentUpdateSchema,
        SwitchboardCreateSchema,
        SwitchboardLoadSummarySchema,
        SwitchboardReadSchema,
        SwitchboardUpdateSchema,
    )
    from core.services.electrical_service import ElectricalService
    from core.services.component_service import ComponentService

# Initialize logger for this module
logger = get_logger(__name__)


class SwitchboardService:
    """
    Service for switchboard operations and electrical distribution management.

    This service provides high-level business logic for switchboard design workflows,
    integrating with the electrical service for load distribution calculations and
    the component service for component validation and management.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the switchboard service.

        Args:
            db_session: SQLAlchemy database session
        """
        self.db_session = db_session
        self.switchboard_repo = SwitchboardRepository(db_session)
        self.feeder_repo = FeederRepository(db_session)
        self.switchboard_component_repo = SwitchboardComponentRepository(db_session)
        self.feeder_component_repo = FeederComponentRepository(db_session)
        self.electrical_service = ElectricalService(db_session)
        self.component_service = ComponentService(db_session)
        logger.debug("SwitchboardService initialized")

    # ============================================================================
    # SWITCHBOARD OPERATIONS
    # ============================================================================

    def create_switchboard(
        self, switchboard_data: SwitchboardCreateSchema
    ) -> SwitchboardReadSchema:
        """
        Create a new switchboard.

        Args:
            switchboard_data: Switchboard creation data

        Returns:
            SwitchboardReadSchema: Created switchboard

        Raises:
            BusinessLogicError: If validation fails
            DatabaseError: If database operation fails
        """
        logger.info(f"Creating switchboard: {switchboard_data.name}")

        try:
            # Validate project exists (this would be done by foreign key constraint)
            # Additional business logic validation can be added here

            # Validate voltage level is reasonable
            if switchboard_data.voltage_level_v > 50000:
                raise BusinessLogicError(
                    code="INVALID_VOLTAGE_LEVEL",
                    detail="Voltage level exceeds maximum allowed value (50kV)",
                )

            # Create switchboard
            switchboard_dict = switchboard_data.model_dump()
            switchboard = self.switchboard_repo.create(switchboard_dict)
            self.db_session.commit()
            self.db_session.refresh(switchboard)

            logger.info(f"Switchboard created successfully: {switchboard.id}")
            return SwitchboardReadSchema.model_validate(switchboard)

        except (BusinessLogicError, NotFoundError):
            self.db_session.rollback()
            raise
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Failed to create switchboard: {e}", exc_info=True)
            raise BaseApplicationException(
                code="SWITCHBOARD_CREATION_ERROR",
                detail=f"Failed to create switchboard: {str(e)}",
            )

    def get_switchboard(self, switchboard_id: int) -> SwitchboardReadSchema:
        """
        Get switchboard by ID.

        Args:
            switchboard_id: Switchboard ID

        Returns:
            SwitchboardReadSchema: Switchboard data

        Raises:
            NotFoundError: If switchboard not found
        """
        logger.debug(f"Retrieving switchboard: {switchboard_id}")

        switchboard = self.switchboard_repo.get_by_id(switchboard_id)
        if not switchboard:
            raise NotFoundError(
                code="SWITCHBOARD_NOT_FOUND",
                detail=f"Switchboard {switchboard_id} not found",
            )

        return SwitchboardReadSchema.model_validate(switchboard)

    def update_switchboard(
        self, switchboard_id: int, update_data: SwitchboardUpdateSchema
    ) -> SwitchboardReadSchema:
        """
        Update switchboard.

        Args:
            switchboard_id: Switchboard ID
            update_data: Update data

        Returns:
            SwitchboardReadSchema: Updated switchboard

        Raises:
            NotFoundError: If switchboard not found
            BusinessLogicError: If validation fails
        """
        logger.info(f"Updating switchboard: {switchboard_id}")

        try:
            # Validate switchboard exists
            existing_switchboard = self.switchboard_repo.get_by_id(switchboard_id)
            if not existing_switchboard:
                raise NotFoundError(
                    code="SWITCHBOARD_NOT_FOUND",
                    detail=f"Switchboard {switchboard_id} not found",
                )

            # Validate voltage level if provided
            if (
                update_data.voltage_level_v is not None
                and update_data.voltage_level_v > 50000
            ):
                raise BusinessLogicError(
                    code="INVALID_VOLTAGE_LEVEL",
                    detail="Voltage level exceeds maximum allowed value (50kV)",
                )

            # Update switchboard
            update_dict = update_data.model_dump(exclude_unset=True)
            updated_switchboard = self.switchboard_repo.update(
                switchboard_id, update_dict
            )
            self.db_session.commit()

            logger.info(f"Switchboard updated successfully: {switchboard_id}")
            return SwitchboardReadSchema.model_validate(updated_switchboard)

        except (BusinessLogicError, NotFoundError):
            self.db_session.rollback()
            raise
        except Exception as e:
            self.db_session.rollback()
            logger.error(
                f"Failed to update switchboard {switchboard_id}: {e}", exc_info=True
            )
            raise BaseApplicationException(
                code="SWITCHBOARD_UPDATE_ERROR",
                detail=f"Failed to update switchboard: {str(e)}",
            )

    def delete_switchboard(
        self, switchboard_id: int, deleted_by_user_id: Optional[int] = None
    ) -> bool:
        """
        Soft delete switchboard.

        Args:
            switchboard_id: Switchboard ID
            deleted_by_user_id: ID of user performing deletion

        Returns:
            bool: True if deleted successfully

        Raises:
            NotFoundError: If switchboard not found
        """
        logger.info(f"Deleting switchboard: {switchboard_id}")

        try:
            success = self.switchboard_repo.soft_delete(
                switchboard_id, deleted_by_user_id
            )
            if success:
                self.db_session.commit()
                logger.info(f"Switchboard deleted successfully: {switchboard_id}")
            else:
                raise NotFoundError(
                    code="SWITCHBOARD_NOT_FOUND",
                    detail=f"Switchboard {switchboard_id} not found",
                )
            return success

        except NotFoundError:
            self.db_session.rollback()
            raise
        except Exception as e:
            self.db_session.rollback()
            logger.error(
                f"Failed to delete switchboard {switchboard_id}: {e}", exc_info=True
            )
            raise BaseApplicationException(
                code="SWITCHBOARD_DELETION_ERROR",
                detail=f"Failed to delete switchboard: {str(e)}",
            )

    def get_switchboards_by_project(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> List[SwitchboardReadSchema]:
        """
        Get switchboards by project ID.

        Args:
            project_id: Project ID
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[SwitchboardReadSchema]: List of switchboards
        """
        logger.debug(f"Retrieving switchboards for project: {project_id}")

        switchboards = self.switchboard_repo.get_by_project_id(project_id, skip, limit)
        return [SwitchboardReadSchema.model_validate(sb) for sb in switchboards]

    # ============================================================================
    # FEEDER OPERATIONS
    # ============================================================================

    def create_feeder(self, feeder_data: FeederCreateSchema) -> FeederReadSchema:
        """
        Create a new feeder.

        Args:
            feeder_data: Feeder creation data

        Returns:
            FeederReadSchema: Created feeder

        Raises:
            BusinessLogicError: If validation fails
            NotFoundError: If switchboard not found
        """
        logger.info(f"Creating feeder: {feeder_data.name}")

        try:
            # Validate switchboard exists
            switchboard = self.switchboard_repo.get_by_id(feeder_data.switchboard_id)
            if not switchboard:
                raise NotFoundError(
                    code="SWITCHBOARD_NOT_FOUND",
                    detail=f"Switchboard {feeder_data.switchboard_id} not found",
                )

            # Create feeder
            feeder_dict = feeder_data.model_dump()
            feeder = self.feeder_repo.create(feeder_dict)
            self.db_session.commit()
            self.db_session.refresh(feeder)

            logger.info(f"Feeder created successfully: {feeder.id}")
            return FeederReadSchema.model_validate(feeder)

        except (BusinessLogicError, NotFoundError):
            self.db_session.rollback()
            raise
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Failed to create feeder: {e}", exc_info=True)
            raise BaseApplicationException(
                code="FEEDER_CREATION_ERROR",
                detail=f"Failed to create feeder: {str(e)}",
            )

    def get_feeder(self, feeder_id: int) -> FeederReadSchema:
        """
        Get feeder by ID.

        Args:
            feeder_id: Feeder ID

        Returns:
            FeederReadSchema: Feeder data

        Raises:
            NotFoundError: If feeder not found
        """
        logger.debug(f"Retrieving feeder: {feeder_id}")

        feeder = self.feeder_repo.get_by_id(feeder_id)
        if not feeder:
            raise NotFoundError(
                code="FEEDER_NOT_FOUND",
                detail=f"Feeder {feeder_id} not found",
            )

        return FeederReadSchema.model_validate(feeder)

    def get_feeders_by_switchboard(
        self, switchboard_id: int, skip: int = 0, limit: int = 100
    ) -> List[FeederReadSchema]:
        """
        Get feeders by switchboard ID.

        Args:
            switchboard_id: Switchboard ID
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[FeederReadSchema]: List of feeders
        """
        logger.debug(f"Retrieving feeders for switchboard: {switchboard_id}")

        feeders = self.feeder_repo.get_by_switchboard_id(switchboard_id, skip, limit)
        return [FeederReadSchema.model_validate(feeder) for feeder in feeders]

    # ============================================================================
    # COMPONENT OPERATIONS
    # ============================================================================

    def add_switchboard_component(
        self, component_data: SwitchboardComponentCreateSchema
    ) -> SwitchboardComponentReadSchema:
        """
        Add component to switchboard.

        Args:
            component_data: Component installation data

        Returns:
            SwitchboardComponentReadSchema: Added component

        Raises:
            BusinessLogicError: If validation fails
            NotFoundError: If switchboard or component not found
        """
        logger.info(
            f"Adding component {component_data.component_id} to switchboard {component_data.switchboard_id}"
        )

        try:
            # Validate switchboard exists
            switchboard = self.switchboard_repo.get_by_id(component_data.switchboard_id)
            if not switchboard:
                raise NotFoundError(
                    code="SWITCHBOARD_NOT_FOUND",
                    detail=f"Switchboard {component_data.switchboard_id} not found",
                )

            # Validate component exists and get details
            component = self.component_service.get_component_details(
                component_data.component_id
            )
            if not component:
                raise NotFoundError(
                    code="COMPONENT_NOT_FOUND",
                    detail=f"Component {component_data.component_id} not found",
                )

            # Log component validation (category validation could be added here if needed)
            logger.debug(
                f"Component {component_data.component_id} validated for switchboard installation"
            )

            # Create switchboard component
            component_dict = component_data.model_dump()
            switchboard_component = self.switchboard_component_repo.create(
                component_dict
            )
            self.db_session.commit()
            self.db_session.refresh(switchboard_component)

            logger.info(
                f"Component added to switchboard successfully: {switchboard_component.id}"
            )
            return SwitchboardComponentReadSchema.model_validate(switchboard_component)

        except (BusinessLogicError, NotFoundError):
            self.db_session.rollback()
            raise
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Failed to add component to switchboard: {e}", exc_info=True)
            raise BaseApplicationException(
                code="SWITCHBOARD_COMPONENT_CREATION_ERROR",
                detail=f"Failed to add component to switchboard: {str(e)}",
            )

    # ============================================================================
    # ELECTRICAL INTEGRATION METHODS
    # ============================================================================

    def get_switchboard_load_summary(
        self, switchboard_id: int
    ) -> SwitchboardLoadSummarySchema:
        """
        Get load distribution summary for a switchboard.

        Args:
            switchboard_id: Switchboard ID

        Returns:
            SwitchboardLoadSummarySchema: Load summary data

        Raises:
            NotFoundError: If switchboard not found
        """
        logger.debug(f"Calculating load summary for switchboard: {switchboard_id}")

        # Validate switchboard exists
        switchboard = self.switchboard_repo.get_by_id(switchboard_id)
        if not switchboard:
            raise NotFoundError(
                code="SWITCHBOARD_NOT_FOUND",
                detail=f"Switchboard {switchboard_id} not found",
            )

        try:
            # Get all feeders for this switchboard
            feeders = self.feeder_repo.get_by_switchboard_id(switchboard_id)
            feeder_count = len(feeders)

            # For now, return basic summary (can be enhanced with electrical calculations)
            # In a real implementation, this would integrate with electrical service
            # to calculate actual load distribution

            # Placeholder calculations - would be replaced with actual electrical calculations
            total_connected_load_kw = 0.0
            total_demand_load_kw = 0.0
            load_factor = 0.8  # Typical load factor
            diversity_factor = 0.9  # Typical diversity factor
            available_capacity_kw = 100.0  # Would be calculated from switchboard rating
            utilization_percent = (
                (total_demand_load_kw / available_capacity_kw) * 100
                if available_capacity_kw > 0
                else 0.0
            )

            return SwitchboardLoadSummarySchema(
                switchboard_id=switchboard_id,
                total_connected_load_kw=total_connected_load_kw,
                total_demand_load_kw=total_demand_load_kw,
                load_factor=load_factor,
                diversity_factor=diversity_factor,
                available_capacity_kw=available_capacity_kw,
                utilization_percent=utilization_percent,
                feeder_count=feeder_count,
            )

        except Exception as e:
            logger.error(
                f"Failed to calculate load summary for switchboard {switchboard_id}: {e}",
                exc_info=True,
            )
            raise BaseApplicationException(
                code="LOAD_CALCULATION_ERROR",
                detail=f"Failed to calculate load summary: {str(e)}",
            )

    def get_switchboard_capacity_analysis(
        self, switchboard_id: int
    ) -> SwitchboardCapacityAnalysisSchema:
        """
        Get capacity analysis for a switchboard.

        Args:
            switchboard_id: Switchboard ID

        Returns:
            SwitchboardCapacityAnalysisSchema: Capacity analysis data

        Raises:
            NotFoundError: If switchboard not found
        """
        logger.debug(f"Performing capacity analysis for switchboard: {switchboard_id}")

        # Validate switchboard exists
        switchboard = self.switchboard_repo.get_by_id(switchboard_id)
        if not switchboard:
            raise NotFoundError(
                code="SWITCHBOARD_NOT_FOUND",
                detail=f"Switchboard {switchboard_id} not found",
            )

        try:
            # Placeholder calculations - would be replaced with actual electrical calculations
            # In a real implementation, this would integrate with electrical service
            # to perform detailed capacity analysis

            rated_capacity_kva = (
                200.0  # Would be calculated from switchboard specifications
            )
            current_load_kva = 120.0  # Would be calculated from connected loads
            available_capacity_kva = rated_capacity_kva - current_load_kva
            capacity_utilization_percent = (current_load_kva / rated_capacity_kva) * 100
            is_overloaded = current_load_kva > rated_capacity_kva

            recommendations = []
            if capacity_utilization_percent > 80:
                recommendations.append("Consider load balancing or capacity upgrade")
            if is_overloaded:
                recommendations.append(
                    "Immediate action required - switchboard is overloaded"
                )
            if capacity_utilization_percent < 50:
                recommendations.append("Switchboard has good available capacity")

            return SwitchboardCapacityAnalysisSchema(
                switchboard_id=switchboard_id,
                rated_capacity_kva=rated_capacity_kva,
                current_load_kva=current_load_kva,
                available_capacity_kva=available_capacity_kva,
                capacity_utilization_percent=capacity_utilization_percent,
                is_overloaded=is_overloaded,
                recommendations=recommendations,
            )

        except Exception as e:
            logger.error(
                f"Failed to perform capacity analysis for switchboard {switchboard_id}: {e}",
                exc_info=True,
            )
            raise BaseApplicationException(
                code="CAPACITY_ANALYSIS_ERROR",
                detail=f"Failed to perform capacity analysis: {str(e)}",
            )
