# Initial Prompt for Next AI Agent

## 🎯 **Mission: Implement Electrical Entity for Ultimate Electrical Designer Backend**

You are continuing the implementation of the Ultimate Electrical Designer backend. **3 out of 4 core entities are complete**, and you're implementing the final core business entity.

## 📋 **Current Status**

### ✅ **COMPLETED (100%)**
- **Project Entity** - Foundation with all 5 layers + comprehensive tests
- **Component Entity** - Catalog management with validation
- **Heat Tracing Entity** - Design workflow with calculations integration (45 tests passing)
- **Calculations Layer** - Engineering calculations (heat loss, electrical sizing)
- **Standards Layer** - TR 50410, IEC 60079-30-1 compliance validation
- **Architecture Foundation** - 5-layer pattern, error handling, logging, testing

### 🎯 **YOUR TASK: Electrical Entity Implementation**

**Priority**: CRITICAL - Final core business domain entity
**Models**: Already exist in `backend/core/models/electrical.py`
- ElectricalNode, CableRoute, CableSegment, LoadCalculation, VoltageDropCalculation

**Required Implementation** (Follow established patterns):
1. **Electrical Schemas** (`backend/core/schemas/electrical_schemas.py`)
2. **Electrical Repository** (`backend/core/repositories/electrical_repository.py`) 
3. **Electrical Service** (`backend/core/services/electrical_service.py`)
4. **Electrical API Routes** (`backend/api/v1/electrical_routes.py`)
5. **Comprehensive Test Suite** (Schema + Repository + Service + API tests)

## 🔗 **Critical Integration Requirements**

### **Calculations Layer Integration**
- Use `CalculationService` for cable sizing and voltage drop calculations
- Apply calculation results to route/segment entities
- Handle calculation errors gracefully

### **Heat Tracing Integration** 
- Connect electrical circuits to heat tracing circuits
- Calculate electrical loads from heat tracing power requirements
- Validate electrical capacity for heat tracing loads

### **Component Integration**
- Reference electrical cables and components from Component entity
- Validate component compatibility with electrical design requirements

## 📚 **Reference Patterns**

**Study these completed implementations:**
- `backend/core/schemas/heat_tracing_schemas.py` - Complex validation patterns
- `backend/core/repositories/heat_tracing_repository.py` - Advanced queries (26 tests passing)
- `backend/core/services/heat_tracing_service.py` - Business logic with calculations integration
- `backend/api/v1/heat_tracing_routes.py` - 15 endpoints with proper error handling

## 🎯 **Success Criteria**

- [ ] Complete electrical design workflow (node/route → cable sizing)
- [ ] Integration with calculations layer for cable sizing/voltage drop
- [ ] Standards compliance validation for electrical designs
- [ ] >90% test coverage across all layers
- [ ] Heat tracing load integration for electrical planning

## 🚀 **Getting Started**

1. **Review existing models** in `backend/core/models/electrical.py`
2. **Study Heat Tracing implementation** as your primary reference
3. **Start with schemas** - Build foundation with comprehensive validation
4. **Test early and often** - Follow the 45-test success pattern from Heat Tracing

## 📖 **Key Documentation**

- `backend/docs/next-agent-prompt.md` - Detailed implementation instructions
- `backend/docs/heat-tracing-implementation-summary.md` - Recent successful example
- `backend/docs/implementation-progress.md` - Overall project status

**You're building the final piece of the core architecture puzzle! The foundation is solid, patterns are established, and you have excellent examples to follow. Focus on creating a seamless electrical design experience that integrates with heat tracing loads.** ⚡
