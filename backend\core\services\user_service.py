# backend/core/services/user_service.py
"""
User Service

This module provides business logic for user operations, including
authentication, user management, and security operations.
"""

import secrets
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional

from passlib.context import CryptContext
from sqlalchemy.orm import Session

try:
    from backend.config.logging_config import get_logger
    from backend.config.settings import get_settings
    from backend.core.errors.exceptions import (
        BaseApplicationException,
        BusinessLogicError,
        NotFoundError,
    )
    from backend.core.repositories.user_repository import (
        UserPreferenceRepository,
        UserRepository,
    )
    from backend.core.schemas.user_schemas import (
        LoginRequestSchema,
        LoginResponseSchema,
        LogoutResponseSchema,
        PasswordChangeRequestSchema,
        PasswordResetConfirmSchema,
        PasswordResetRequestSchema,
        UserCreateSchema,
        UserPreferenceCreateSchema,
        UserPreferenceReadSchema,
        UserPreferenceUpdateSchema,
        UserReadSchema,
        UserSessionSchema,
        UserUpdateSchema,
    )
except ImportError:
    # For testing and relative imports
    import sys
    import os

    sys.path.insert(
        0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
    from config.logging_config import get_logger
    from config.settings import get_settings
    from core.errors.exceptions import (
        BaseApplicationException,
        BusinessLogicError,
        NotFoundError,
    )
    from core.repositories.user_repository import (
        UserPreferenceRepository,
        UserRepository,
    )
    from core.schemas.user_schemas import (
        LoginRequestSchema,
        LoginResponseSchema,
        LogoutResponseSchema,
        PasswordChangeRequestSchema,
        PasswordResetConfirmSchema,
        PasswordResetRequestSchema,
        UserCreateSchema,
        UserPreferenceCreateSchema,
        UserPreferenceReadSchema,
        UserPreferenceUpdateSchema,
        UserReadSchema,
        UserSessionSchema,
        UserUpdateSchema,
    )

# Initialize logger for this module
logger = get_logger(__name__)

# Initialize password context for secure hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Get application settings
settings = get_settings()


class UserService:
    """
    Service for user operations and authentication management.

    This service provides high-level business logic for user management workflows,
    including authentication, password management, and user preferences.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the user service.

        Args:
            db_session: SQLAlchemy database session
        """
        self.db_session = db_session
        self.user_repo = UserRepository(db_session)
        self.preference_repo = UserPreferenceRepository(db_session)
        logger.debug("UserService initialized")

    # ============================================================================
    # USER MANAGEMENT OPERATIONS
    # ============================================================================

    def create_user(self, user_data: UserCreateSchema) -> UserReadSchema:
        """
        Create a new user with secure password hashing.

        Args:
            user_data: User creation data

        Returns:
            UserReadSchema: Created user

        Raises:
            BusinessLogicError: If validation fails
            DatabaseError: If database operation fails
        """
        logger.info(f"Creating user: {user_data.name}")

        try:
            # Check if user with email already exists
            if user_data.email:
                existing_user = self.user_repo.get_by_email(user_data.email)
                if existing_user:
                    raise BusinessLogicError(
                        code="EMAIL_ALREADY_EXISTS",
                        detail=f"User with email {user_data.email} already exists",
                    )

            # Hash the password
            password_hash = pwd_context.hash(user_data.password)

            # Create user data without plain password
            user_dict = user_data.model_dump(exclude={"password"})
            user_dict["password_hash"] = password_hash

            # Create user
            user = self.user_repo.create(user_dict)
            self.db_session.commit()
            self.db_session.refresh(user)

            logger.info(f"User created successfully: {user.id}")
            return UserReadSchema.model_validate(user)

        except (BusinessLogicError, NotFoundError):
            self.db_session.rollback()
            raise
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Failed to create user: {e}", exc_info=True)
            raise BaseApplicationException(
                code="USER_CREATION_ERROR",
                detail=f"Failed to create user: {str(e)}",
            )

    def get_user(self, user_id: int) -> UserReadSchema:
        """
        Get user by ID.

        Args:
            user_id: User ID

        Returns:
            UserReadSchema: User data

        Raises:
            NotFoundError: If user not found
        """
        logger.debug(f"Retrieving user: {user_id}")

        user = self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )

        return UserReadSchema.model_validate(user)

    def get_user_by_email(self, email: str) -> UserReadSchema:
        """
        Get user by email address.

        Args:
            email: User email address

        Returns:
            UserReadSchema: User data

        Raises:
            NotFoundError: If user not found
        """
        logger.debug(f"Retrieving user by email: {email}")

        user = self.user_repo.get_by_email(email)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User with email {email} not found or inactive",
            )

        return UserReadSchema.model_validate(user)

    def update_user(
        self, user_id: int, update_data: UserUpdateSchema
    ) -> UserReadSchema:
        """
        Update user information.

        Args:
            user_id: User ID
            update_data: Update data

        Returns:
            UserReadSchema: Updated user

        Raises:
            NotFoundError: If user not found
            BusinessLogicError: If validation fails
        """
        logger.info(f"Updating user: {user_id}")

        try:
            # Validate user exists
            existing_user = self.user_repo.get_by_id(user_id)
            if not existing_user or not existing_user.is_active:
                raise NotFoundError(
                    code="USER_NOT_FOUND",
                    detail=f"User {user_id} not found or inactive",
                )

            # Check email uniqueness if email is being updated
            if update_data.email and update_data.email != existing_user.email:
                email_user = self.user_repo.get_by_email(update_data.email)
                if email_user and email_user.id != user_id:
                    raise BusinessLogicError(
                        code="EMAIL_ALREADY_EXISTS",
                        detail=f"Email {update_data.email} is already in use",
                    )

            # Update user
            update_dict = update_data.model_dump(exclude_unset=True)
            updated_user = self.user_repo.update(user_id, update_dict)
            self.db_session.commit()

            logger.info(f"User updated successfully: {user_id}")
            return UserReadSchema.model_validate(updated_user)

        except (BusinessLogicError, NotFoundError):
            self.db_session.rollback()
            raise
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Failed to update user {user_id}: {e}", exc_info=True)
            raise BaseApplicationException(
                code="USER_UPDATE_ERROR",
                detail=f"Failed to update user: {str(e)}",
            )

    def deactivate_user(self, user_id: int) -> bool:
        """
        Deactivate a user account.

        Args:
            user_id: User ID

        Returns:
            bool: True if deactivated successfully

        Raises:
            NotFoundError: If user not found
        """
        logger.info(f"Deactivating user: {user_id}")

        try:
            success = self.user_repo.deactivate_user(user_id)
            if success:
                self.db_session.commit()
                logger.info(f"User deactivated successfully: {user_id}")
            else:
                raise NotFoundError(
                    code="USER_NOT_FOUND",
                    detail=f"User {user_id} not found",
                )
            return success

        except NotFoundError:
            self.db_session.rollback()
            raise
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"Failed to deactivate user {user_id}: {e}", exc_info=True)
            raise BaseApplicationException(
                code="USER_DEACTIVATION_ERROR",
                detail=f"Failed to deactivate user: {str(e)}",
            )

    def get_users(self, skip: int = 0, limit: int = 100) -> List[UserReadSchema]:
        """
        Get list of active users.

        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[UserReadSchema]: List of users
        """
        logger.debug(f"Retrieving users: skip={skip}, limit={limit}")

        users = self.user_repo.get_active_users(skip, limit)
        return [UserReadSchema.model_validate(user) for user in users]

    # ============================================================================
    # AUTHENTICATION OPERATIONS
    # ============================================================================

    def authenticate_user(self, login_data: LoginRequestSchema) -> UserReadSchema:
        """
        Authenticate user with email and password.

        Args:
            login_data: Login credentials

        Returns:
            UserReadSchema: Authenticated user

        Raises:
            BusinessLogicError: If authentication fails
        """
        logger.debug(f"Authenticating user: {login_data.email}")

        try:
            # Get user by email
            user = self.user_repo.get_by_email(login_data.email)
            if not user or not user.is_active:
                raise BusinessLogicError(
                    code="INVALID_CREDENTIALS",
                    detail="Invalid email or password",
                )

            # Verify password
            if not user.password_hash or not pwd_context.verify(
                login_data.password, user.password_hash
            ):
                raise BusinessLogicError(
                    code="INVALID_CREDENTIALS",
                    detail="Invalid email or password",
                )

            logger.info(f"User authenticated successfully: {user.id}")
            return UserReadSchema.model_validate(user)

        except BusinessLogicError:
            raise
        except Exception as e:
            logger.error(
                f"Authentication error for {login_data.email}: {e}", exc_info=True
            )
            raise BaseApplicationException(
                code="AUTHENTICATION_ERROR",
                detail="Authentication failed due to system error",
            )

    def generate_access_token(self, user: UserReadSchema) -> str:
        """
        Generate JWT access token for user.

        Args:
            user: User data

        Returns:
            str: JWT access token

        Note:
            This is a placeholder implementation. In a real system, you would use
            a proper JWT library like python-jose or PyJWT to generate tokens.
        """
        logger.debug(f"Generating access token for user: {user.id}")

        # Placeholder token generation - replace with actual JWT implementation
        # In a real implementation, you would:
        # 1. Create JWT payload with user ID, email, expiration
        # 2. Sign with SECRET_KEY using JWT_ALGORITHM
        # 3. Return the signed token

        # Placeholder token (in real implementation, use JWT library)
        # The token would include user.id, user.email, and expiration time
        token = f"placeholder_token_{user.id}_{secrets.token_urlsafe(32)}"

        logger.debug(f"Access token generated for user: {user.id}")
        return token

    def login(self, login_data: LoginRequestSchema) -> LoginResponseSchema:
        """
        Perform user login and return authentication response.

        Args:
            login_data: Login credentials

        Returns:
            LoginResponseSchema: Login response with token and user data

        Raises:
            BusinessLogicError: If authentication fails
        """
        logger.info(f"Login attempt for: {login_data.email}")

        try:
            # Authenticate user
            user = self.authenticate_user(login_data)

            # Generate access token
            access_token = self.generate_access_token(user)

            # Create login response
            response = LoginResponseSchema(
                access_token=access_token,
                token_type="bearer",
                expires_in=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
                * 60,  # Convert to seconds
                user=user,
            )

            logger.info(f"Login successful for user: {user.id}")
            return response

        except (BusinessLogicError, BaseApplicationException):
            raise
        except Exception as e:
            logger.error(f"Login error for {login_data.email}: {e}", exc_info=True)
            raise BaseApplicationException(
                code="LOGIN_ERROR",
                detail="Login failed due to system error",
            )

    def logout(self, user_id: int) -> LogoutResponseSchema:
        """
        Perform user logout.

        Args:
            user_id: User ID

        Returns:
            LogoutResponseSchema: Logout confirmation

        Note:
            In a real implementation, this would invalidate the user's tokens
            and clean up any session data.
        """
        logger.info(f"Logout for user: {user_id}")

        try:
            # In a real implementation, you would:
            # 1. Invalidate the user's access tokens
            # 2. Clean up session data
            # 3. Log the logout event

            logger.info(f"Logout successful for user: {user_id}")
            return LogoutResponseSchema(message="Successfully logged out")

        except Exception as e:
            logger.error(f"Logout error for user {user_id}: {e}", exc_info=True)
            raise BaseApplicationException(
                code="LOGOUT_ERROR",
                detail="Logout failed due to system error",
            )

    # ============================================================================
    # PASSWORD MANAGEMENT OPERATIONS
    # ============================================================================

    def change_password(
        self, user_id: int, password_data: PasswordChangeRequestSchema
    ) -> bool:
        """
        Change user password.

        Args:
            user_id: User ID
            password_data: Password change data

        Returns:
            bool: True if password changed successfully

        Raises:
            NotFoundError: If user not found
            BusinessLogicError: If current password is invalid
        """
        logger.info(f"Password change request for user: {user_id}")

        try:
            # Get user
            user = self.user_repo.get_by_id(user_id)
            if not user or not user.is_active:
                raise NotFoundError(
                    code="USER_NOT_FOUND",
                    detail=f"User {user_id} not found or inactive",
                )

            # Verify current password
            if not user.password_hash or not pwd_context.verify(
                password_data.current_password, user.password_hash
            ):
                raise BusinessLogicError(
                    code="INVALID_CURRENT_PASSWORD",
                    detail="Current password is incorrect",
                )

            # Hash new password
            new_password_hash = pwd_context.hash(password_data.new_password)

            # Update password
            success = self.user_repo.update_password(user_id, new_password_hash)
            if success:
                self.db_session.commit()
                logger.info(f"Password changed successfully for user: {user_id}")
            else:
                raise NotFoundError(
                    code="USER_NOT_FOUND",
                    detail=f"User {user_id} not found",
                )

            return success

        except (BusinessLogicError, NotFoundError):
            self.db_session.rollback()
            raise
        except Exception as e:
            self.db_session.rollback()
            logger.error(
                f"Password change error for user {user_id}: {e}", exc_info=True
            )
            raise BaseApplicationException(
                code="PASSWORD_CHANGE_ERROR",
                detail="Password change failed due to system error",
            )

    # ============================================================================
    # USER PREFERENCES OPERATIONS
    # ============================================================================

    def get_user_preferences(self, user_id: int) -> Optional[UserPreferenceReadSchema]:
        """
        Get user preferences.

        Args:
            user_id: User ID

        Returns:
            Optional[UserPreferenceReadSchema]: User preferences or None if not found

        Raises:
            NotFoundError: If user not found
        """
        logger.debug(f"Retrieving preferences for user: {user_id}")

        # Validate user exists
        user = self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )

        preferences = self.preference_repo.get_by_user_id(user_id)
        if preferences:
            return UserPreferenceReadSchema.model_validate(preferences)
        return None

    def create_or_update_user_preferences(
        self, user_id: int, preferences_data: UserPreferenceUpdateSchema
    ) -> UserPreferenceReadSchema:
        """
        Create or update user preferences.

        Args:
            user_id: User ID
            preferences_data: Preferences data

        Returns:
            UserPreferenceReadSchema: Created or updated preferences

        Raises:
            NotFoundError: If user not found
        """
        logger.info(f"Creating/updating preferences for user: {user_id}")

        try:
            # Validate user exists
            user = self.user_repo.get_by_id(user_id)
            if not user or not user.is_active:
                raise NotFoundError(
                    code="USER_NOT_FOUND",
                    detail=f"User {user_id} not found or inactive",
                )

            # Create or update preferences
            preferences_dict = preferences_data.model_dump(exclude_unset=True)
            preferences = self.preference_repo.create_or_update_preferences(
                user_id, preferences_dict
            )
            self.db_session.commit()
            self.db_session.refresh(preferences)

            logger.info(f"Preferences updated successfully for user: {user_id}")
            return UserPreferenceReadSchema.model_validate(preferences)

        except NotFoundError:
            self.db_session.rollback()
            raise
        except Exception as e:
            self.db_session.rollback()
            logger.error(
                f"Failed to update preferences for user {user_id}: {e}", exc_info=True
            )
            raise BaseApplicationException(
                code="PREFERENCES_UPDATE_ERROR",
                detail=f"Failed to update preferences: {str(e)}",
            )

    def delete_user_preferences(
        self, user_id: int, deleted_by_user_id: Optional[int] = None
    ) -> bool:
        """
        Soft delete user preferences.

        Args:
            user_id: User ID
            deleted_by_user_id: ID of user performing deletion

        Returns:
            bool: True if deleted successfully

        Raises:
            NotFoundError: If user not found
        """
        logger.info(f"Deleting preferences for user: {user_id}")

        try:
            # Validate user exists
            user = self.user_repo.get_by_id(user_id)
            if not user or not user.is_active:
                raise NotFoundError(
                    code="USER_NOT_FOUND",
                    detail=f"User {user_id} not found or inactive",
                )

            success = self.preference_repo.soft_delete_preferences(
                user_id, deleted_by_user_id
            )
            if success:
                self.db_session.commit()
                logger.info(f"Preferences deleted successfully for user: {user_id}")
            else:
                logger.debug(f"No preferences found for user: {user_id}")

            return success

        except NotFoundError:
            self.db_session.rollback()
            raise
        except Exception as e:
            self.db_session.rollback()
            logger.error(
                f"Failed to delete preferences for user {user_id}: {e}", exc_info=True
            )
            raise BaseApplicationException(
                code="PREFERENCES_DELETION_ERROR",
                detail=f"Failed to delete preferences: {str(e)}",
            )

    # ============================================================================
    # UTILITY METHODS
    # ============================================================================

    def search_users(
        self, search_term: str, skip: int = 0, limit: int = 100
    ) -> List[UserReadSchema]:
        """
        Search users by name or email.

        Args:
            search_term: Search term
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List[UserReadSchema]: List of matching users
        """
        logger.debug(f"Searching users with term: {search_term}")

        users = self.user_repo.search_users(search_term, skip, limit)
        return [UserReadSchema.model_validate(user) for user in users]

    def count_active_users(self) -> int:
        """
        Count total number of active users.

        Returns:
            int: Number of active users
        """
        logger.debug("Counting active users")
        return self.user_repo.count_active_users()
