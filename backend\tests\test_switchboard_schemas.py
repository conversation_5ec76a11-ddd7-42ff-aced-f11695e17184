# backend/tests/test_switchboard_schemas.py
"""
Tests for Switchboard Schemas

This module tests the validation and serialization of switchboard-related schemas.
"""

import pytest
from pydantic import ValidationError

from backend.core.schemas.switchboard_schemas import (
    SwitchboardCreateSchema,
    SwitchboardUpdateSchema,
    FeederCreateSchema,
    SwitchboardComponentCreateSchema,
    FeederComponentCreateSchema,
    SwitchboardLoadSummarySchema,
    SwitchboardCapacityAnalysisSchema,
)


class TestSwitchboardSchemas:
    """Test switchboard schema validation."""

    def test_switchboard_create_schema_valid(self):
        """Test valid switchboard creation data."""
        data = {
            "name": "Main Distribution Board",
            "project_id": 1,
            "location": "Main electrical room",
            "voltage_level_v": 415,
            "number_of_phases": 3,
            "type": "MAIN",
        }
        
        schema = SwitchboardCreateSchema(**data)
        assert schema.name == "Main Distribution Board"
        assert schema.voltage_level_v == 415
        assert schema.number_of_phases == 3

    def test_switchboard_create_schema_invalid_voltage(self):
        """Test switchboard creation with invalid voltage."""
        data = {
            "name": "Test Board",
            "project_id": 1,
            "voltage_level_v": 60000,  # Exceeds maximum
            "number_of_phases": 3,
        }
        
        with pytest.raises(ValidationError) as exc_info:
            SwitchboardCreateSchema(**data)
        
        assert "voltage_level_v" in str(exc_info.value)

    def test_switchboard_create_schema_invalid_phases(self):
        """Test switchboard creation with invalid number of phases."""
        data = {
            "name": "Test Board",
            "project_id": 1,
            "voltage_level_v": 415,
            "number_of_phases": 2,  # Invalid - must be 1 or 3
        }
        
        with pytest.raises(ValidationError) as exc_info:
            SwitchboardCreateSchema(**data)
        
        assert "Number of phases must be 1 or 3" in str(exc_info.value)

    def test_switchboard_create_schema_empty_name(self):
        """Test switchboard creation with empty name."""
        data = {
            "name": "   ",  # Empty after strip
            "project_id": 1,
            "voltage_level_v": 415,
            "number_of_phases": 3,
        }
        
        with pytest.raises(ValidationError) as exc_info:
            SwitchboardCreateSchema(**data)
        
        assert "Switchboard name cannot be empty" in str(exc_info.value)

    def test_switchboard_update_schema_partial(self):
        """Test switchboard update with partial data."""
        data = {
            "name": "Updated Board Name",
            "location": "New location",
        }
        
        schema = SwitchboardUpdateSchema(**data)
        assert schema.name == "Updated Board Name"
        assert schema.location == "New location"
        assert schema.voltage_level_v is None  # Not provided

    def test_feeder_create_schema_valid(self):
        """Test valid feeder creation data."""
        data = {
            "name": "Feeder 01 - Heat Tracing",
            "switchboard_id": 1,
        }
        
        schema = FeederCreateSchema(**data)
        assert schema.name == "Feeder 01 - Heat Tracing"
        assert schema.switchboard_id == 1

    def test_feeder_create_schema_empty_name(self):
        """Test feeder creation with empty name."""
        data = {
            "name": "",
            "switchboard_id": 1,
        }
        
        with pytest.raises(ValidationError) as exc_info:
            FeederCreateSchema(**data)
        
        assert "Feeder name cannot be empty" in str(exc_info.value)

    def test_switchboard_component_create_schema_valid(self):
        """Test valid switchboard component creation data."""
        data = {
            "switchboard_id": 1,
            "component_id": 15,
            "quantity": 2,
            "position": "Row 1, Position 3-4",
        }
        
        schema = SwitchboardComponentCreateSchema(**data)
        assert schema.switchboard_id == 1
        assert schema.component_id == 15
        assert schema.quantity == 2
        assert schema.position == "Row 1, Position 3-4"

    def test_switchboard_component_create_schema_invalid_quantity(self):
        """Test switchboard component creation with invalid quantity."""
        data = {
            "switchboard_id": 1,
            "component_id": 15,
            "quantity": 0,  # Invalid - must be >= 1
        }
        
        with pytest.raises(ValidationError) as exc_info:
            SwitchboardComponentCreateSchema(**data)
        
        assert "quantity" in str(exc_info.value)

    def test_feeder_component_create_schema_valid(self):
        """Test valid feeder component creation data."""
        data = {
            "feeder_id": 1,
            "component_id": 25,
            "quantity": 1,
            "position": "Output Terminal",
        }
        
        schema = FeederComponentCreateSchema(**data)
        assert schema.feeder_id == 1
        assert schema.component_id == 25
        assert schema.quantity == 1
        assert schema.position == "Output Terminal"

    def test_switchboard_load_summary_schema_valid(self):
        """Test valid switchboard load summary data."""
        data = {
            "switchboard_id": 1,
            "total_connected_load_kw": 50.0,
            "total_demand_load_kw": 40.0,
            "load_factor": 0.8,
            "diversity_factor": 0.9,
            "available_capacity_kw": 60.0,
            "utilization_percent": 66.7,
            "feeder_count": 5,
        }
        
        schema = SwitchboardLoadSummarySchema(**data)
        assert schema.switchboard_id == 1
        assert schema.total_connected_load_kw == 50.0
        assert schema.feeder_count == 5

    def test_switchboard_capacity_analysis_schema_valid(self):
        """Test valid switchboard capacity analysis data."""
        data = {
            "switchboard_id": 1,
            "rated_capacity_kva": 200.0,
            "current_load_kva": 120.0,
            "available_capacity_kva": 80.0,
            "capacity_utilization_percent": 60.0,
            "is_overloaded": False,
            "recommendations": ["Consider load balancing"],
        }
        
        schema = SwitchboardCapacityAnalysisSchema(**data)
        assert schema.switchboard_id == 1
        assert schema.rated_capacity_kva == 200.0
        assert schema.is_overloaded is False
        assert len(schema.recommendations) == 1

    def test_switchboard_capacity_analysis_schema_overloaded(self):
        """Test switchboard capacity analysis with overload condition."""
        data = {
            "switchboard_id": 1,
            "rated_capacity_kva": 100.0,
            "current_load_kva": 120.0,
            "available_capacity_kva": -20.0,
            "capacity_utilization_percent": 120.0,
            "is_overloaded": True,
            "recommendations": ["Immediate action required - switchboard is overloaded"],
        }
        
        schema = SwitchboardCapacityAnalysisSchema(**data)
        assert schema.is_overloaded is True
        assert schema.capacity_utilization_percent == 120.0
        assert "overloaded" in schema.recommendations[0]


class TestSwitchboardSchemaEdgeCases:
    """Test edge cases and boundary conditions."""

    def test_switchboard_minimum_voltage(self):
        """Test switchboard with minimum voltage."""
        data = {
            "name": "Low Voltage Board",
            "project_id": 1,
            "voltage_level_v": 1,  # Minimum allowed
            "number_of_phases": 1,
        }
        
        schema = SwitchboardCreateSchema(**data)
        assert schema.voltage_level_v == 1

    def test_switchboard_maximum_voltage(self):
        """Test switchboard with maximum voltage."""
        data = {
            "name": "High Voltage Board",
            "project_id": 1,
            "voltage_level_v": 50000,  # Maximum allowed
            "number_of_phases": 3,
        }
        
        schema = SwitchboardCreateSchema(**data)
        assert schema.voltage_level_v == 50000

    def test_component_maximum_quantity(self):
        """Test component with maximum quantity."""
        data = {
            "switchboard_id": 1,
            "component_id": 15,
            "quantity": 100,  # Maximum allowed
        }
        
        schema = SwitchboardComponentCreateSchema(**data)
        assert schema.quantity == 100

    def test_position_whitespace_handling(self):
        """Test position field whitespace handling."""
        data = {
            "switchboard_id": 1,
            "component_id": 15,
            "quantity": 1,
            "position": "  Row 1  ",  # Whitespace should be stripped
        }
        
        schema = SwitchboardComponentCreateSchema(**data)
        assert schema.position == "Row 1"

    def test_optional_fields_none(self):
        """Test schemas with optional fields set to None."""
        data = {
            "name": "Basic Board",
            "project_id": 1,
            "voltage_level_v": 415,
            "number_of_phases": 3,
            "location": None,
            "type": None,
        }
        
        schema = SwitchboardCreateSchema(**data)
        assert schema.location is None
        assert schema.type is None
