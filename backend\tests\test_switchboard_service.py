# backend/tests/test_switchboard_service.py
"""
Tests for Switchboard Service

This module tests the business logic of switchboard operations.
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.errors.exceptions import BusinessLogicError, NotFoundError
from core.schemas.switchboard_schemas import (
    SwitchboardCreateSchema,
    SwitchboardUpdateSchema,
    FeederCreateSchema,
    SwitchboardComponentCreateSchema,
)
from core.services.switchboard_service import SwitchboardService


class TestSwitchboardService:
    """Test switchboard service business logic."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def mock_switchboard_repo(self):
        """Create a mock switchboard repository."""
        return Mock()

    @pytest.fixture
    def mock_feeder_repo(self):
        """Create a mock feeder repository."""
        return Mock()

    @pytest.fixture
    def mock_component_repo(self):
        """Create a mock component repository."""
        return Mock()

    @pytest.fixture
    def mock_electrical_service(self):
        """Create a mock electrical service."""
        return Mock()

    @pytest.fixture
    def mock_component_service(self):
        """Create a mock component service."""
        return Mock()

    @pytest.fixture
    def switchboard_service(self, mock_db_session):
        """Create a switchboard service with mocked dependencies."""
        with patch.multiple(
            "backend.core.services.switchboard_service",
            SwitchboardRepository=Mock(),
            FeederRepository=Mock(),
            SwitchboardComponentRepository=Mock(),
            FeederComponentRepository=Mock(),
            ElectricalService=Mock(),
            ComponentService=Mock(),
        ):
            return SwitchboardService(mock_db_session)

    def test_create_switchboard_success(self, switchboard_service, mock_db_session):
        """Test successful switchboard creation."""
        # Arrange
        switchboard_data = SwitchboardCreateSchema(
            name="Test Switchboard",
            project_id=1,
            voltage_level_v=415,
            number_of_phases=3,
        )

        mock_switchboard = Mock()
        mock_switchboard.id = 1
        mock_switchboard.name = "Test Switchboard"

        switchboard_service.switchboard_repo.create.return_value = mock_switchboard

        # Act
        result = switchboard_service.create_switchboard(switchboard_data)

        # Assert
        assert result.name == "Test Switchboard"
        switchboard_service.switchboard_repo.create.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_db_session.refresh.assert_called_once_with(mock_switchboard)

    def test_create_switchboard_invalid_voltage(self, switchboard_service):
        """Test switchboard creation with invalid voltage level."""
        # Arrange
        switchboard_data = SwitchboardCreateSchema(
            name="Test Switchboard",
            project_id=1,
            voltage_level_v=60000,  # Exceeds maximum
            number_of_phases=3,
        )

        # Act & Assert
        with pytest.raises(BusinessLogicError) as exc_info:
            switchboard_service.create_switchboard(switchboard_data)

        assert exc_info.value.code == "INVALID_VOLTAGE_LEVEL"
        assert "50kV" in exc_info.value.detail

    def test_get_switchboard_success(self, switchboard_service):
        """Test successful switchboard retrieval."""
        # Arrange
        mock_switchboard = Mock()
        mock_switchboard.id = 1
        mock_switchboard.name = "Test Switchboard"

        switchboard_service.switchboard_repo.get_by_id.return_value = mock_switchboard

        # Act
        result = switchboard_service.get_switchboard(1)

        # Assert
        assert result.name == "Test Switchboard"
        switchboard_service.switchboard_repo.get_by_id.assert_called_once_with(1)

    def test_get_switchboard_not_found(self, switchboard_service):
        """Test switchboard retrieval when not found."""
        # Arrange
        switchboard_service.switchboard_repo.get_by_id.return_value = None

        # Act & Assert
        with pytest.raises(NotFoundError) as exc_info:
            switchboard_service.get_switchboard(999)

        assert exc_info.value.code == "SWITCHBOARD_NOT_FOUND"
        assert "999" in exc_info.value.detail

    def test_update_switchboard_success(self, switchboard_service, mock_db_session):
        """Test successful switchboard update."""
        # Arrange
        update_data = SwitchboardUpdateSchema(
            name="Updated Switchboard",
            voltage_level_v=480,
        )

        mock_existing = Mock()
        mock_updated = Mock()
        mock_updated.id = 1
        mock_updated.name = "Updated Switchboard"

        switchboard_service.switchboard_repo.get_by_id.return_value = mock_existing
        switchboard_service.switchboard_repo.update.return_value = mock_updated

        # Act
        result = switchboard_service.update_switchboard(1, update_data)

        # Assert
        assert result.name == "Updated Switchboard"
        switchboard_service.switchboard_repo.update.assert_called_once()
        mock_db_session.commit.assert_called_once()

    def test_update_switchboard_invalid_voltage(self, switchboard_service):
        """Test switchboard update with invalid voltage level."""
        # Arrange
        update_data = SwitchboardUpdateSchema(
            voltage_level_v=60000,  # Exceeds maximum
        )

        mock_existing = Mock()
        switchboard_service.switchboard_repo.get_by_id.return_value = mock_existing

        # Act & Assert
        with pytest.raises(BusinessLogicError) as exc_info:
            switchboard_service.update_switchboard(1, update_data)

        assert exc_info.value.code == "INVALID_VOLTAGE_LEVEL"

    def test_delete_switchboard_success(self, switchboard_service, mock_db_session):
        """Test successful switchboard deletion."""
        # Arrange
        switchboard_service.switchboard_repo.soft_delete.return_value = True

        # Act
        result = switchboard_service.delete_switchboard(1, 2)

        # Assert
        assert result is True
        switchboard_service.switchboard_repo.soft_delete.assert_called_once_with(1, 2)
        mock_db_session.commit.assert_called_once()

    def test_delete_switchboard_not_found(self, switchboard_service):
        """Test switchboard deletion when not found."""
        # Arrange
        switchboard_service.switchboard_repo.soft_delete.return_value = False

        # Act & Assert
        with pytest.raises(NotFoundError) as exc_info:
            switchboard_service.delete_switchboard(999)

        assert exc_info.value.code == "SWITCHBOARD_NOT_FOUND"

    def test_create_feeder_success(self, switchboard_service, mock_db_session):
        """Test successful feeder creation."""
        # Arrange
        feeder_data = FeederCreateSchema(
            name="Test Feeder",
            switchboard_id=1,
        )

        mock_switchboard = Mock()
        mock_feeder = Mock()
        mock_feeder.id = 1
        mock_feeder.name = "Test Feeder"

        switchboard_service.switchboard_repo.get_by_id.return_value = mock_switchboard
        switchboard_service.feeder_repo.create.return_value = mock_feeder

        # Act
        result = switchboard_service.create_feeder(feeder_data)

        # Assert
        assert result.name == "Test Feeder"
        switchboard_service.feeder_repo.create.assert_called_once()
        mock_db_session.commit.assert_called_once()

    def test_create_feeder_switchboard_not_found(self, switchboard_service):
        """Test feeder creation when switchboard not found."""
        # Arrange
        feeder_data = FeederCreateSchema(
            name="Test Feeder",
            switchboard_id=999,
        )

        switchboard_service.switchboard_repo.get_by_id.return_value = None

        # Act & Assert
        with pytest.raises(NotFoundError) as exc_info:
            switchboard_service.create_feeder(feeder_data)

        assert exc_info.value.code == "SWITCHBOARD_NOT_FOUND"

    def test_add_switchboard_component_success(
        self, switchboard_service, mock_db_session
    ):
        """Test successful switchboard component addition."""
        # Arrange
        component_data = SwitchboardComponentCreateSchema(
            switchboard_id=1,
            component_id=15,
            quantity=2,
            position="Test Position",
        )

        mock_switchboard = Mock()
        mock_component_details = Mock()
        mock_switchboard_component = Mock()
        mock_switchboard_component.id = 1

        switchboard_service.switchboard_repo.get_by_id.return_value = mock_switchboard
        switchboard_service.component_service.get_component_details.return_value = (
            mock_component_details
        )
        switchboard_service.switchboard_component_repo.create.return_value = (
            mock_switchboard_component
        )

        # Act
        result = switchboard_service.add_switchboard_component(component_data)

        # Assert
        assert result.id == 1
        switchboard_service.switchboard_component_repo.create.assert_called_once()
        mock_db_session.commit.assert_called_once()

    def test_add_switchboard_component_switchboard_not_found(self, switchboard_service):
        """Test component addition when switchboard not found."""
        # Arrange
        component_data = SwitchboardComponentCreateSchema(
            switchboard_id=999,
            component_id=15,
            quantity=2,
            position="Test Position",
        )

        switchboard_service.switchboard_repo.get_by_id.return_value = None

        # Act & Assert
        with pytest.raises(NotFoundError) as exc_info:
            switchboard_service.add_switchboard_component(component_data)

        assert exc_info.value.code == "SWITCHBOARD_NOT_FOUND"

    def test_add_switchboard_component_component_not_found(self, switchboard_service):
        """Test component addition when component not found."""
        # Arrange
        component_data = SwitchboardComponentCreateSchema(
            switchboard_id=1,
            component_id=999,
            quantity=2,
            position="Test Position",
        )

        mock_switchboard = Mock()
        switchboard_service.switchboard_repo.get_by_id.return_value = mock_switchboard
        switchboard_service.component_service.get_component_details.return_value = None

        # Act & Assert
        with pytest.raises(NotFoundError) as exc_info:
            switchboard_service.add_switchboard_component(component_data)

        assert exc_info.value.code == "COMPONENT_NOT_FOUND"

    def test_get_switchboard_load_summary_success(self, switchboard_service):
        """Test successful load summary calculation."""
        # Arrange
        mock_switchboard = Mock()
        mock_feeders = [Mock(), Mock(), Mock()]

        switchboard_service.switchboard_repo.get_by_id.return_value = mock_switchboard
        switchboard_service.feeder_repo.get_by_switchboard_id.return_value = (
            mock_feeders
        )

        # Act
        result = switchboard_service.get_switchboard_load_summary(1)

        # Assert
        assert result.switchboard_id == 1
        assert result.feeder_count == 3
        assert isinstance(result.total_connected_load_kw, float)
        assert isinstance(result.utilization_percent, float)

    def test_get_switchboard_capacity_analysis_success(self, switchboard_service):
        """Test successful capacity analysis."""
        # Arrange
        mock_switchboard = Mock()
        switchboard_service.switchboard_repo.get_by_id.return_value = mock_switchboard

        # Act
        result = switchboard_service.get_switchboard_capacity_analysis(1)

        # Assert
        assert result.switchboard_id == 1
        assert isinstance(result.rated_capacity_kva, float)
        assert isinstance(result.is_overloaded, bool)
        assert isinstance(result.recommendations, list)
